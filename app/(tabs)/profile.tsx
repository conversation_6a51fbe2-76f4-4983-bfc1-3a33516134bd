import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useThemeColor } from '@/hooks/useThemeColor';
import React from 'react';
import { Alert, Platform, StatusBar, StyleSheet, TouchableOpacity } from 'react-native';

export default function ProfileScreen() {
  const primaryColor = useThemeColor({}, 'primary');
  const surfaceColor = useThemeColor({}, 'surface');
  const { themeMode, toggleTheme } = require('@/contexts/ThemeContext').useTheme();

  const getThemeDisplayText = () => {
    switch (themeMode) {
      case 'light': return 'Light';
      case 'dark': return 'Dark';
      case 'system': return 'System';
      default: return 'System';
    }
  };

  const handleDarkModePress = () => {
    toggleTheme();
  };

  const handleAboutPress = () => {
    Alert.alert(
      'About BCS Question Bank',
      'BCS Question Bank v1.0.0\n\nA comprehensive offline-first app for BCS exam preparation.\n\n• 46+ BCS exam sets\n• 5000+ practice questions\n• Offline support\n• Free forever\n\nDeveloped with ❤️ for BCS aspirants',
      [{ text: 'OK' }]
    );
  };

  return (
    <ThemedView style={styles.container}>
      {/* Header */}
      <ThemedView style={[styles.header, { backgroundColor: primaryColor }]} lightColor={primaryColor} darkColor={primaryColor}>
        <ThemedView style={styles.headerContent} lightColor="transparent" darkColor="transparent">
          <ThemedText type="title" style={styles.headerTitle}>
            Profile & Settings
          </ThemedText>
          <ThemedText type="caption" style={styles.headerSubtitle}>
            Manage your account and preferences
          </ThemedText>
        </ThemedView>
      </ThemedView>

      {/* Content */}
      <ThemedView style={styles.content}>
        {/* Profile Section */}
        <ThemedView style={styles.section}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>Account</ThemedText>
          <ThemedView style={[styles.profileCard  , { backgroundColor: surfaceColor }]}>
            <ThemedView style={[styles.profileInfo , { backgroundColor: 'transparent' }]}>
              <ThemedView style={styles.avatar} >
                <ThemedText style={styles.avatarText}>👤</ThemedText>
              </ThemedView>
              <ThemedView style={[styles.userInfo, { backgroundColor: 'transparent' }]}>
                <ThemedText type="defaultSemiBold" style={styles.userName}>BCS Candidate</ThemedText>
                <ThemedText type="secondary" style={styles.userEmail}>Ready to excel in BCS</ThemedText>
              </ThemedView>
            </ThemedView>
          </ThemedView>
        </ThemedView>

        {/* Settings Section */}
        <ThemedView style={styles.section}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>Settings</ThemedText>

          <TouchableOpacity style={[styles.settingItem, { backgroundColor: surfaceColor }]} activeOpacity={0.7} onPress={handleDarkModePress}>
            <ThemedText style={styles.settingIcon}>🌙</ThemedText>
            <ThemedView style={styles.settingContent}>
              <ThemedText type="defaultSemiBold">Theme</ThemedText>
              <ThemedText type="secondary">{getThemeDisplayText()}</ThemedText>
            </ThemedView>
            <ThemedText style={styles.settingArrow}>›</ThemedText>
          </TouchableOpacity>

          <TouchableOpacity style={[styles.settingItem, { backgroundColor: surfaceColor }]} activeOpacity={0.7} onPress={handleAboutPress}>
            <ThemedText style={styles.settingIcon}>ℹ️</ThemedText>
            <ThemedView style={styles.settingContent}>
              <ThemedText type="defaultSemiBold">About</ThemedText>
              <ThemedText type="secondary">App info & support</ThemedText>
            </ThemedView>
            <ThemedText style={styles.settingArrow}>›</ThemedText>
          </TouchableOpacity>
        </ThemedView>
      </ThemedView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: Platform.OS === 'android' ? (StatusBar.currentHeight || 0) + 20 : 50,
    paddingBottom: 30,
    paddingHorizontal: 20,
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 30,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 8,
  },
  headerContent: {
    alignItems: 'center',
  },
  headerTitle: {
    color: 'white',
    textAlign: 'center',
    marginBottom: 8,
    letterSpacing: 0.5,
  },
  headerSubtitle: {
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    marginBottom: 16,
    paddingHorizontal: 4,
  },
  profileCard: {
    borderRadius: 16,
    padding: 20,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  profileInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  avatarText: {
    fontSize: 24,
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    marginBottom: 4,
  },
  userEmail: {
    fontSize: 14,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  settingIcon: {
    fontSize: 20,
    marginRight: 16,
    width: 24,
    textAlign: 'center',
  },
  settingContent: {
    flex: 1,
  },
  settingArrow: {
    fontSize: 18,
    opacity: 0.5,
  },
});
