import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useThemeColor } from '@/hooks/useThemeColor';
import React from 'react';
import { Platform, StatusBar, StyleSheet } from 'react-native';

export default function BookmarksScreen() {
  const primaryColor = useThemeColor({}, 'primary');

  return (
    <ThemedView style={styles.container}>
      {/* Header */}
      <ThemedView style={[styles.header, { backgroundColor: primaryColor }]} lightColor={primaryColor} darkColor={primaryColor}>
        <ThemedView style={styles.headerContent} lightColor="transparent" darkColor="transparent">
          <ThemedText type="title" style={styles.headerTitle}>
            Bookmarks
          </ThemedText>
          <ThemedText type="caption" style={styles.headerSubtitle}>
            Your saved questions and favorites
          </ThemedText>
        </ThemedView>
      </ThemedView>

      {/* Content */}
      <ThemedView style={styles.content}>
        <ThemedView style={styles.emptyState}>
          <ThemedText type="heading" style={styles.emptyTitle}>📖 No Bookmarks Yet</ThemedText>
          <ThemedText type="secondary" style={styles.emptyDescription}>
            Start practicing questions and bookmark your favorites to see them here.
          </ThemedText>
        </ThemedView>
      </ThemedView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: Platform.OS === 'android' ? (StatusBar.currentHeight || 0) + 20 : 50,
    paddingBottom: 30,
    paddingHorizontal: 20,
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 30,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 8,
  },
  headerContent: {
    alignItems: 'center',
  },
  headerTitle: {
    color: 'white',
    textAlign: 'center',
    marginBottom: 8,
    letterSpacing: 0.5,
  },
  headerSubtitle: {
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyTitle: {
    textAlign: 'center',
    marginBottom: 16,
  },
  emptyDescription: {
    textAlign: 'center',
    lineHeight: 22,
  },
});
