import { ModernLoader } from '@/components/ModernLoader';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useThemeColor } from '@/hooks/useThemeColor';
import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
  ScrollView,
  StyleSheet,
  TouchableOpacity
} from 'react-native';

interface BCSSet {
  title: string;
  sub: string;
  file: string;
}

export default function ExploreScreen() {
  const [bcsSets, setBcsSets] = useState<BCSSet[]>([]);
  const [loading, setLoading] = useState(true);

  // Get theme colors
  const primaryColor = useThemeColor({}, 'primary');
  const surfaceColor = useThemeColor({}, 'surface');
  const shadowColor = useThemeColor({}, 'shadow');

  useEffect(() => {
    fetchBCSSets();
  }, []);

  const fetchBCSSets = async () => {
    try {
      setLoading(true);
      const response = await fetch('https://bcs-qb.github.io/index/allbcsquestions.json');
      const data = await response.json();
      // Sort in descending order (newest first)
      const sortedSets = data.allbcsquestions.sort((a: BCSSet, b: BCSSet) => {
        const aNum = parseInt(a.title.match(/\d+/)?.[0] || '0');
        const bNum = parseInt(b.title.match(/\d+/)?.[0] || '0');
        return bNum - aNum;
      });
      setBcsSets(sortedSets);
    } catch (error) {
      console.error('Error fetching BCS sets:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleBCSSetPress = (bcsSet: BCSSet) => {
    router.push({
      pathname: '/questions',
      params: {
        title: bcsSet.title,
        subtitle: bcsSet.sub,
        fileUrl: bcsSet.file
      }
    });
  };

  return (
    <ThemedView style={styles.container}>
      <ThemedView style={[styles.header, { backgroundColor: primaryColor }]} lightColor={primaryColor} darkColor={primaryColor}>
        <ThemedView style={styles.headerContent} lightColor="transparent" darkColor="transparent">
          <ThemedText type="title" style={styles.headerTitle}>
            BCS Question Sets
          </ThemedText>
          <ThemedText type="caption" style={styles.headerSubtitle}>
            {loading ? 'Loading...' : `${bcsSets.length} BCS Sets Available`}
          </ThemedText>
        </ThemedView>
      </ThemedView>

      {loading ? (
        <ModernLoader
          message="Loading BCS Sets"
          submessage="Fetching all available BCS question sets..."
        />
      ) : (
        <ScrollView style={styles.content}>
          <ThemedView style={styles.cardsContainer}>
            {bcsSets.map((bcsSet, index) => (
              <TouchableOpacity
                key={index}
                onPress={() => handleBCSSetPress(bcsSet)}
              >
                <ThemedView style={[styles.card, { backgroundColor: surfaceColor, shadowColor }]} type="card">
                  <ThemedView style={styles.cardContent}>
                    <ThemedText type="subheading" style={styles.cardTitle}>{bcsSet.title}</ThemedText>
                    <ThemedText type="secondary" style={styles.cardSubtitle}>{bcsSet.sub}</ThemedText>
                  </ThemedView>
                </ThemedView>
              </TouchableOpacity>
            ))}
          </ThemedView>

        </ScrollView>
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 50, // Will be updated to use Platform.OS check
    paddingBottom: 30,
    paddingHorizontal: 20,
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 30,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 8,
  },
  headerContent: {
    alignItems: 'center',
  },
  headerTitle: {
    color: 'white',
    textAlign: 'center',
    marginBottom: 8,
    letterSpacing: 0.5,
  },
  headerSubtitle: {
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
  },
  content: {
    flex: 1,
  },
  cardsContainer: {
    padding: 20,
    gridAutoColumns: '1fr',
  },
  card: {
    marginBottom: 16,
    borderRadius: 20,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
  },
  cardContent: {
    padding: 20,
  },
  cardTitle: {
    marginBottom: 8,
    letterSpacing: 0.3,
  },
  cardSubtitle: {
    lineHeight: 22,
  },
});
