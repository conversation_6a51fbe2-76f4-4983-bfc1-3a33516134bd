import AsyncStorage from '@react-native-async-storage/async-storage';

export interface Question {
  question_number: number;
  question_text: string;
  options: {
    A: string;
    B: string;
    C: string;
    D: string;
  };
  correct_answer: string;
  explanation: string;
  subject: string;
}

export interface BCSSet {
  title: string;
  sub: string;
  file: string;
  downloadStatus?: 'not_downloaded' | 'downloading' | 'downloaded' | 'error';
  lastUpdated?: string;
  size?: number;
}

class DataManager {
  private static instance: DataManager;
  private readonly CACHE_DURATION = 7 * 24 * 60 * 60 * 1000; // 7 days
  private readonly BASE_URL = 'https://bcs-qb.github.io/index';
  private readonly STORAGE_KEYS = {
    BCS_SETS: 'bcs_sets',
    QUESTIONS_PREFIX: 'questions_',
    DOWNLOAD_STATUS: 'download_status_',
    LAST_SYNC: 'last_sync'
  };

  public static getInstance(): DataManager {
    if (!DataManager.instance) {
      DataManager.instance = new DataManager();
    }
    return DataManager.instance;
  }

  // ===== BCS SETS MANAGEMENT =====
  
  /**
   * Get BCS sets with offline-first approach
   */
  async getBCSSets(forceRefresh = false): Promise<BCSSet[]> {
    try {
      // Try to get cached data first
      if (!forceRefresh) {
        const cachedSets = await this.getCachedBCSSets();
        if (cachedSets && cachedSets.length > 0) {
          // Check if cache is still valid
          const lastSync = await AsyncStorage.getItem(this.STORAGE_KEYS.LAST_SYNC);
          if (lastSync && Date.now() - parseInt(lastSync) < this.CACHE_DURATION) {
            return cachedSets;
          }
        }
      }

      // Fetch fresh data from remote
      const response = await fetch(`${this.BASE_URL}/allbcsquestions.json`);
      const data = await response.json();
      
      const bcsSets: BCSSet[] = data.allbcsquestions.map((set: any) => ({
        ...set,
        downloadStatus: 'not_downloaded'
      }));

      // Cache the fresh data
      await this.cacheBCSSets(bcsSets);
      await AsyncStorage.setItem(this.STORAGE_KEYS.LAST_SYNC, Date.now().toString());

      return bcsSets;
    } catch (error) {
      console.error('Error fetching BCS sets:', error);
      
      // Fallback to cached data if available
      const cachedSets = await this.getCachedBCSSets();
      if (cachedSets && cachedSets.length > 0) {
        return cachedSets;
      }
      
      throw new Error('No data available offline');
    }
  }

  private async getCachedBCSSets(): Promise<BCSSet[] | null> {
    try {
      const cached = await AsyncStorage.getItem(this.STORAGE_KEYS.BCS_SETS);
      return cached ? JSON.parse(cached) : null;
    } catch (error) {
      console.error('Error reading cached BCS sets:', error);
      return null;
    }
  }

  private async cacheBCSSets(sets: BCSSet[]): Promise<void> {
    try {
      await AsyncStorage.setItem(this.STORAGE_KEYS.BCS_SETS, JSON.stringify(sets));
    } catch (error) {
      console.error('Error caching BCS sets:', error);
    }
  }

  // ===== QUESTIONS MANAGEMENT =====

  /**
   * Get questions with offline-first approach
   */
  async getQuestions(fileUrl: string, forceRefresh = false): Promise<Question[]> {
    const cacheKey = this.getQuestionsCacheKey(fileUrl);
    
    try {
      // Try cached data first
      if (!forceRefresh) {
        const cachedQuestions = await this.getCachedQuestions(cacheKey);
        if (cachedQuestions && cachedQuestions.length > 0) {
          return cachedQuestions;
        }
      }

      // Fetch from remote
      const response = await fetch(fileUrl);
      const data = await response.json();
      const questions: Question[] = data.questions || [];

      // Cache the questions
      await this.cacheQuestions(cacheKey, questions);

      return questions;
    } catch (error) {
      console.error('Error fetching questions:', error);
      
      // Fallback to cached data
      const cachedQuestions = await this.getCachedQuestions(cacheKey);
      if (cachedQuestions && cachedQuestions.length > 0) {
        return cachedQuestions;
      }
      
      throw new Error('Questions not available offline');
    }
  }

  /**
   * Download questions for offline use
   */
  async downloadQuestions(bcsSet: BCSSet, onProgress?: (progress: number) => void): Promise<void> {
    const cacheKey = this.getQuestionsCacheKey(bcsSet.file);

    try {
      // Update download status
      await this.updateDownloadStatus(bcsSet.file, 'downloading');

      onProgress?.(0);

      const response = await fetch(bcsSet.file);
      const data = await response.json();
      const questions: Question[] = data.questions || [];

      onProgress?.(50);

      // Cache questions
      await this.cacheQuestions(cacheKey, questions);

      onProgress?.(100);

      // Update download status
      await this.updateDownloadStatus(bcsSet.file, 'downloaded');

    } catch (error) {
      console.error('Error downloading questions:', error);
      await this.updateDownloadStatus(bcsSet.file, 'error');
      throw error;
    }
  }

  /**
   * Auto-download all BCS sets in background (one by one)
   */
  async autoDownloadAllSets(
    onProgress?: (current: number, total: number, currentSet: string) => void,
    onComplete?: () => void,
    onError?: (error: string, setTitle: string) => void
  ): Promise<void> {
    try {
      // Get all BCS sets
      const bcsSets = await this.getBCSSets();
      const totalSets = bcsSets.length;
      let downloadedCount = 0;
      let skippedCount = 0;

      console.log(`Starting auto-download of ${totalSets} BCS sets...`);

      for (let i = 0; i < bcsSets.length; i++) {
        const bcsSet = bcsSets[i];

        try {
          // Check if already downloaded
          const status = await this.getDownloadStatus(bcsSet.file);
          if (status === 'downloaded') {
            skippedCount++;
            console.log(`Skipping ${bcsSet.title} - already downloaded`);
            onProgress?.(i + 1, totalSets, `${bcsSet.title} (already downloaded)`);
            continue;
          }

          console.log(`Downloading ${bcsSet.title}... (${i + 1}/${totalSets})`);
          onProgress?.(i + 1, totalSets, bcsSet.title);

          // Download with retry logic
          await this.downloadWithRetry(bcsSet, 3);
          downloadedCount++;

          console.log(`✅ Downloaded ${bcsSet.title}`);

          // Small delay between downloads to prevent overwhelming the server
          await this.delay(500);

        } catch (error) {
          console.error(`❌ Failed to download ${bcsSet.title}:`, error);
          onError?.(error instanceof Error ? error.message : 'Download failed', bcsSet.title);
          // Continue with next set even if one fails
        }
      }

      console.log(`Auto-download complete: ${downloadedCount} downloaded, ${skippedCount} skipped`);
      onComplete?.();

    } catch (error) {
      console.error('Error in auto-download:', error);
      onError?.(error instanceof Error ? error.message : 'Auto-download failed', 'System');
    }
  }

  /**
   * Download with retry logic
   */
  private async downloadWithRetry(bcsSet: BCSSet, maxRetries: number): Promise<void> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        await this.downloadQuestions(bcsSet);
        return; // Success, exit retry loop
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Download failed');
        console.log(`Attempt ${attempt}/${maxRetries} failed for ${bcsSet.title}: ${lastError.message}`);

        if (attempt < maxRetries) {
          // Wait before retry (exponential backoff)
          await this.delay(1000 * attempt);
        }
      }
    }

    throw lastError;
  }

  /**
   * Utility delay function
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Check if auto-download should run
   */
  async shouldAutoDownload(): Promise<boolean> {
    try {
      // Check if we're online first
      const online = await this.isOnline();
      if (!online) {
        console.log('🌐 Auto-download skipped: Device is offline');
        return false;
      }

      // Check if there are incomplete downloads (resume scenario)
      const hasIncompleteDownloads = await this.hasIncompleteDownloads();
      if (hasIncompleteDownloads) {
        console.log('🔄 Auto-download resuming: Found incomplete downloads');
        return true;
      }

      // Check if it's been more than 24 hours since last auto-download
      const lastAutoDownload = await AsyncStorage.getItem('last_auto_download');
      if (lastAutoDownload) {
        const timeSinceLastDownload = Date.now() - parseInt(lastAutoDownload);
        const twentyFourHours = 24 * 60 * 60 * 1000;
        const hoursAgo = Math.floor(timeSinceLastDownload / (60 * 60 * 1000));

        if (timeSinceLastDownload < twentyFourHours) {
          console.log(`⏰ Auto-download skipped: Last download was ${hoursAgo} hours ago (need 24+ hours)`);
          return false; // Too soon for another auto-download
        }
      }

      console.log('✅ Auto-download conditions met - starting download...');
      return true;
    } catch (error) {
      console.error('Error checking auto-download conditions:', error);
      return false;
    }
  }

  /**
   * Check if there are incomplete downloads
   */
  async hasIncompleteDownloads(): Promise<boolean> {
    try {
      const bcsSets = await this.getBCSSets();
      for (const bcsSet of bcsSets) {
        const status = await this.getDownloadStatus(bcsSet.file);
        if (status === 'not_downloaded' || status === 'error') {
          return true;
        }
      }
      return false;
    } catch (error) {
      console.error('Error checking incomplete downloads:', error);
      return false;
    }
  }

  /**
   * Mark auto-download as completed
   */
  async markAutoDownloadComplete(): Promise<void> {
    try {
      await AsyncStorage.setItem('last_auto_download', Date.now().toString());
      console.log('✅ Auto-download marked as complete');
    } catch (error) {
      console.error('Error marking auto-download complete:', error);
    }
  }

  /**
   * Get auto-download status for debugging
   */
  async getAutoDownloadStatus(): Promise<{
    lastDownload: string | null;
    hoursAgo: number;
    isOnline: boolean;
    shouldRun: boolean;
  }> {
    try {
      const lastAutoDownload = await AsyncStorage.getItem('last_auto_download');
      const hoursAgo = lastAutoDownload
        ? Math.floor((Date.now() - parseInt(lastAutoDownload)) / (60 * 60 * 1000))
        : -1;
      const isOnline = await this.isOnline();
      const shouldRun = await this.shouldAutoDownload();

      return {
        lastDownload: lastAutoDownload ? new Date(parseInt(lastAutoDownload)).toLocaleString() : null,
        hoursAgo,
        isOnline,
        shouldRun
      };
    } catch (error) {
      console.error('Error getting auto-download status:', error);
      return {
        lastDownload: null,
        hoursAgo: -1,
        isOnline: false,
        shouldRun: false
      };
    }
  }

  private async getCachedQuestions(cacheKey: string): Promise<Question[] | null> {
    try {
      const cached = await AsyncStorage.getItem(cacheKey);
      return cached ? JSON.parse(cached) : null;
    } catch (error) {
      console.error('Error reading cached questions:', error);
      return null;
    }
  }

  private async cacheQuestions(cacheKey: string, questions: Question[]): Promise<void> {
    try {
      await AsyncStorage.setItem(cacheKey, JSON.stringify(questions));
    } catch (error) {
      console.error('Error caching questions:', error);
    }
  }

  private getQuestionsCacheKey(fileUrl: string): string {
    const fileName = fileUrl.split('/').pop()?.replace('.json', '') || 'unknown';
    return `${this.STORAGE_KEYS.QUESTIONS_PREFIX}${fileName}`;
  }

  // ===== DOWNLOAD STATUS MANAGEMENT =====

  async getDownloadStatus(fileUrl: string): Promise<string> {
    try {
      const status = await AsyncStorage.getItem(`${this.STORAGE_KEYS.DOWNLOAD_STATUS}${fileUrl}`);
      return status || 'not_downloaded';
    } catch (error) {
      return 'not_downloaded';
    }
  }

  private async updateDownloadStatus(fileUrl: string, status: string): Promise<void> {
    try {
      await AsyncStorage.setItem(`${this.STORAGE_KEYS.DOWNLOAD_STATUS}${fileUrl}`, status);
    } catch (error) {
      console.error('Error updating download status:', error);
    }
  }

  // ===== UTILITY METHODS =====

  /**
   * Get storage usage information
   */
  async getStorageInfo(): Promise<{ totalSize: number; questionSets: number }> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const questionKeys = keys.filter(key => key.startsWith(this.STORAGE_KEYS.QUESTIONS_PREFIX));
      
      let totalSize = 0;
      for (const key of questionKeys) {
        const data = await AsyncStorage.getItem(key);
        if (data) {
          totalSize += new Blob([data]).size;
        }
      }

      return {
        totalSize,
        questionSets: questionKeys.length
      };
    } catch (error) {
      console.error('Error getting storage info:', error);
      return { totalSize: 0, questionSets: 0 };
    }
  }

  /**
   * Clear all cached data
   */
  async clearCache(): Promise<void> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => 
        key.startsWith(this.STORAGE_KEYS.QUESTIONS_PREFIX) ||
        key === this.STORAGE_KEYS.BCS_SETS ||
        key === this.STORAGE_KEYS.LAST_SYNC ||
        key.startsWith(this.STORAGE_KEYS.DOWNLOAD_STATUS)
      );
      
      await AsyncStorage.multiRemove(cacheKeys);
    } catch (error) {
      console.error('Error clearing cache:', error);
    }
  }

  /**
   * Check if device is online
   */
  async isOnline(): Promise<boolean> {
    try {
      const response = await fetch(`${this.BASE_URL}/allbcsquestions.json`, {
        method: 'HEAD',
        timeout: 5000
      });
      return response.ok;
    } catch (error) {
      return false;
    }
  }
}

export default DataManager;
